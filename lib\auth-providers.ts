/**
 * 认证提供商配置
 * 模块化的认证提供商实现
 */

import CredentialsProvider from "next-auth/providers/credentials"
import GoogleProvider from "next-auth/providers/google"
import { compare } from "bcryptjs"
import { prisma } from "@/lib/db"
import { getAuthConfig } from "./auth-config"
import type { Provider } from "next-auth/providers"

// 用户验证函数 - 支持邮箱和用户名登录
export async function validateUser(identifier: string, password: string) {
  console.log('🔍 开始验证用户:', identifier)

  // 判断输入的是邮箱还是用户名
  const isEmail = identifier.includes('@')
  
  // 根据输入类型查找用户
  const user = await prisma.user.findUnique({
    where: isEmail ? { email: identifier } : { username: identifier },
    include: {
      userRoles: {
        include: {
          role: true
        }
      },
      currencyBalance: true
    }
  })

  if (!user || !user.hashedPassword) {
    console.log('❌ 用户不存在或密码为空')
    throw new Error("用户不存在或密码错误")
  }

  // 验证密码
  const isPasswordValid = await compare(password, user.hashedPassword)
  if (!isPasswordValid) {
    console.log('❌ 密码验证失败')
    throw new Error("用户不存在或密码错误")
  }

  // 检查账户状态
  if (!user.isActive) {
    console.log('❌ 账户已被禁用')
    throw new Error("账户已被禁用")
  }

  const config = getAuthConfig()

  // 只有邮箱注册的用户才需要邮箱验证
  // 用户名注册的用户（email为null）不需要邮箱验证
  if (config.requireEmailVerification && user.email && !user.emailVerified) {
    console.log('❌ 邮箱未验证')
    throw new Error("请先验证邮箱后再登录。如果您没有收到验证邮件，请联系客服或重新注册。")
  }

  // 更新最后登录时间
  await prisma.user.update({
    where: { id: user.id },
    data: { lastLoginAt: new Date() }
  })

  console.log('✅ 用户验证成功:', user.email || user.username)
  return user
}

// Google OAuth 用户处理
async function handleGoogleUser(profile: any, account: any) {
  console.log('🔍 处理 Google 用户:', profile.email)

  // 查找现有用户
  let user = await prisma.user.findUnique({
    where: { email: profile.email },
    include: {
      userRoles: {
        include: { role: true }
      },
      currencyBalance: true
    }
  })

  // 如果用户不存在，创建新用户
  if (!user) {
    console.log('📝 创建新的 Google 用户')
    
    // 生成唯一用户名（基于邮箱前缀）
    const baseUsername = profile.email.split('@')[0]
    let username = baseUsername
    let counter = 1
    
    // 确保用户名唯一
    while (await prisma.user.findUnique({ where: { username } })) {
      username = `${baseUsername}${counter}`
      counter++
    }

    user = await prisma.user.create({
      data: {
        email: profile.email,
        username: username,
        name: profile.name,
        avatar: profile.picture,
        emailVerified: new Date(), // Google 账户默认已验证
        isActive: true,
      },
      include: {
        userRoles: {
          include: {
            role: true
          }
        },
        currencyBalance: true
      }
    })

    // 分配默认角色
    const registeredRole = await prisma.role.findUnique({
      where: { name: 'registered' }
    })

    if (registeredRole) {
      await prisma.userRole.create({
        data: {
          userId: user.id,
          roleId: registeredRole.id
        }
      })
    }

    // 创建虚拟货币余额
    await prisma.currencyBalance.create({
      data: {
        userId: user.id,
        balance: 100, // 新用户赠送100欢乐豆
        frozenBalance: 0,
        totalEarned: 100,
        totalSpent: 0
      }
    })

    console.log('✅ Google 用户创建成功')
  } else {
    // 更新现有用户信息
    await prisma.user.update({
      where: { id: user.id },
      data: {
        lastLoginAt: new Date(),
        // 如果用户没有头像，使用 Google 头像
        avatar: user.avatar || profile.picture,
        // 如果邮箱未验证，标记为已验证
        emailVerified: user.emailVerified || new Date()
      }
    })
    console.log('✅ 现有 Google 用户登录成功')
  }

  // 重新查询用户以获取完整的关联数据
  const fullUser = await prisma.user.findUnique({
    where: { id: user.id },
    include: {
      userRoles: {
        include: {
          role: true
        }
      },
      currencyBalance: true
    }
  })

  if (!fullUser) {
    throw new Error('用户查询失败')
  }

  return fullUser
}

// Credentials Provider 配置
export function createCredentialsProvider(): Provider {
  const config = getAuthConfig()
  
  return CredentialsProvider({
    id: "credentials",
    name: "credentials",
    credentials: {
      identifier: { 
        label: config.allowEmailLogin && config.allowUsernameLogin 
          ? "邮箱或用户名" 
          : config.allowEmailLogin 
            ? "邮箱" 
            : "用户名", 
        type: "text" 
      },
      password: { label: "密码", type: "password" }
    },
    async authorize(credentials) {
      if (!credentials?.identifier || !credentials?.password) {
        console.log('❌ 登录信息不完整')
        throw new Error('请输入完整的登录信息')
      }

      try {
        const user = await validateUser(
          credentials.identifier as string,
          credentials.password as string
        )

        // 返回 NextAuth.js 期望的用户对象格式
        return {
          id: user.id,
          email: user.email,
          name: user.name,
          image: user.avatar,
        }
      } catch (error) {
        const errorMessage = error instanceof Error ? error.message : '用户验证失败'
        console.error('❌ 用户验证失败:', errorMessage)
        // 直接抛出错误，让NextAuth.js处理
        throw new Error(errorMessage)
      }
    }
  })
}

// Google Provider 配置
export function createGoogleProvider(): Provider | null {
  const config = getAuthConfig()
  
  if (!config.googleOAuth.enabled || !config.googleOAuth.clientId || !config.googleOAuth.clientSecret) {
    return null
  }

  return GoogleProvider({
    clientId: config.googleOAuth.clientId,
    clientSecret: config.googleOAuth.clientSecret,
    profile: async (profile, tokens) => {
      console.log('🔍 Google profile:', profile)
      
      // 处理 Google 用户
      const user = await handleGoogleUser(profile, tokens)
      
      return {
        id: user.id,
        email: user.email,
        name: user.name,
        image: user.avatar,
        emailVerified: user.emailVerified
      }
    }
  })
}

// 获取所有启用的认证提供商
export function getEnabledProviders(): Provider[] {
  const config = getAuthConfig()
  const providers: Provider[] = []

  // 添加 Credentials Provider（支持邮箱和用户名登录）
  if (config.enabledProviders.includes('credentials')) {
    providers.push(createCredentialsProvider())
  }

  // 添加 Google Provider
  if (config.enabledProviders.includes('google')) {
    const googleProvider = createGoogleProvider()
    if (googleProvider) {
      providers.push(googleProvider)
    }
  }

  return providers
}

// 导出认证提供商信息（用于前端显示）
export function getProviderDisplayInfo() {
  const config = getAuthConfig()
  
  return {
    credentials: {
      enabled: config.enabledProviders.includes('credentials'),
      allowEmail: config.allowEmailLogin,
      allowUsername: config.allowUsernameLogin,
      label: config.allowEmailLogin && config.allowUsernameLogin 
        ? '邮箱/用户名登录' 
        : config.allowEmailLogin 
          ? '邮箱登录' 
          : '用户名登录'
    },
    google: {
      enabled: config.googleOAuth.enabled,
      label: 'Google 登录'
    }
  }
}
